<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>麦克风测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .button:hover {
            background: #40a9ff;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>麦克风兼容性测试</h1>
        <p>此页面用于测试不同浏览器（特别是Edge）的麦克风访问兼容性</p>
        
        <div class="test-section">
            <h3>浏览器信息</h3>
            <div id="browserInfo" class="result info"></div>
        </div>

        <div class="test-section">
            <h3>基础麦克风测试</h3>
            <button class="button" onclick="testBasicMicrophone()">测试基础麦克风访问</button>
            <div id="basicResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>渐进式约束测试</h3>
            <button class="button" onclick="testProgressiveConstraints()">测试渐进式约束</button>
            <div id="progressiveResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>设备枚举测试</h3>
            <button class="button" onclick="testDeviceEnumeration()">枚举音频设备</button>
            <div id="deviceResult" class="result"></div>
        </div>
    </div>

    <script>
        // 检测浏览器信息
        function getBrowserInfo() {
            const userAgent = navigator.userAgent;
            const isEdge = /Edg/.test(userAgent);
            const isChrome = /Chrome/.test(userAgent) && !/Edg/.test(userAgent);
            const isFirefox = /Firefox/.test(userAgent);
            const isSafari = /Safari/.test(userAgent) && !/Chrome/.test(userAgent);
            
            return {
                isEdge,
                isChrome,
                isFirefox,
                isSafari,
                name: isEdge ? 'Edge' : isChrome ? 'Chrome' : isFirefox ? 'Firefox' : isSafari ? 'Safari' : 'Unknown',
                userAgent
            };
        }

        // 获取音频约束
        function getAudioConstraints() {
            const browserInfo = getBrowserInfo();
            
            if (browserInfo.isEdge) {
                return [
                    { audio: true },
                    {
                        audio: {
                            echoCancellation: true,
                            noiseSuppression: true,
                            autoGainControl: true
                        }
                    },
                    {
                        audio: {
                            sampleRate: 44100,
                            echoCancellation: true,
                            noiseSuppression: true,
                            autoGainControl: true
                        }
                    }
                ];
            }
            
            return [
                {
                    audio: {
                        channelCount: 1,
                        sampleRate: 24000,
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                },
                {
                    audio: {
                        channelCount: 1,
                        sampleRate: 44100,
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                },
                {
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                },
                { audio: true }
            ];
        }

        // 显示浏览器信息
        function displayBrowserInfo() {
            const info = getBrowserInfo();
            const element = document.getElementById('browserInfo');
            element.textContent = `浏览器: ${info.name}\nUser Agent: ${info.userAgent}`;
        }

        // 测试基础麦克风访问
        async function testBasicMicrophone() {
            const resultElement = document.getElementById('basicResult');
            resultElement.textContent = '正在测试...';
            resultElement.className = 'result info';

            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                stream.getTracks().forEach(track => track.stop());
                
                resultElement.textContent = '✅ 基础麦克风访问成功';
                resultElement.className = 'result success';
            } catch (error) {
                resultElement.textContent = `❌ 基础麦克风访问失败:\n错误类型: ${error.name}\n错误信息: ${error.message}`;
                resultElement.className = 'result error';
            }
        }

        // 测试渐进式约束
        async function testProgressiveConstraints() {
            const resultElement = document.getElementById('progressiveResult');
            resultElement.textContent = '正在测试...';
            resultElement.className = 'result info';

            const constraints = getAudioConstraints();
            let results = [];

            for (let i = 0; i < constraints.length; i++) {
                const constraint = constraints[i];
                try {
                    const stream = await navigator.mediaDevices.getUserMedia(constraint);
                    stream.getTracks().forEach(track => track.stop());
                    
                    results.push(`✅ 约束 ${i + 1} 成功: ${JSON.stringify(constraint, null, 2)}`);
                    break; // 成功后停止测试
                } catch (error) {
                    results.push(`❌ 约束 ${i + 1} 失败 (${error.name}): ${JSON.stringify(constraint, null, 2)}`);
                }
            }

            resultElement.textContent = results.join('\n\n');
            resultElement.className = results.some(r => r.includes('✅')) ? 'result success' : 'result error';
        }

        // 测试设备枚举
        async function testDeviceEnumeration() {
            const resultElement = document.getElementById('deviceResult');
            resultElement.textContent = '正在枚举设备...';
            resultElement.className = 'result info';

            try {
                const devices = await navigator.mediaDevices.enumerateDevices();
                const audioInputs = devices.filter(device => device.kind === 'audioinput');
                
                let result = `找到 ${audioInputs.length} 个音频输入设备:\n\n`;
                audioInputs.forEach((device, index) => {
                    result += `设备 ${index + 1}:\n`;
                    result += `  ID: ${device.deviceId}\n`;
                    result += `  标签: ${device.label || '(需要权限才能显示)'}\n`;
                    result += `  组ID: ${device.groupId}\n\n`;
                });

                resultElement.textContent = result;
                resultElement.className = 'result success';
            } catch (error) {
                resultElement.textContent = `❌ 设备枚举失败:\n错误类型: ${error.name}\n错误信息: ${error.message}`;
                resultElement.className = 'result error';
            }
        }

        // 页面加载时显示浏览器信息
        window.onload = function() {
            displayBrowserInfo();
        };
    </script>
</body>
</html>
