<template>
    <div class="clone-com" @click.stop="onClose">
        <div class="dialog-ctrl" @click.stop="isClosing = false" :class="{ 'closing': isClosing }">
            <div class="dlg-header">
                <h3 class="dlg-title">声音复刻</h3>
                <button class="dlg-close-btn" @click.stop="onClose" aria-label="关闭">
                    <CloseOutlined />
                </button>
            </div>

            <div class="dlg-content">
                <!-- 麦克风检测失败提示 -->
                <div v-if="!microphoneAvailable" class="microphone-error">
                    <div class="error-icon">
                        <svg viewBox="0 0 24 24" width="64" height="64" fill="currentColor">
                            <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z" />
                            <path
                                d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z" />
                            <path
                                d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" />
                        </svg>
                    </div>
                    <div class="error-content">
                        <h3 class="error-title">无法访问麦克风</h3>
                        <div class="error-message">
                            <p v-if="microphoneError === 'permission'">
                                请允许浏览器访问您的麦克风权限，然后刷新页面重试
                            </p>
                            <p v-else-if="microphoneError === 'notfound'">
                                未检测到麦克风设备，请连接麦克风后重试
                            </p>
                            <p v-else-if="microphoneError === 'notsupported'">
                                您的浏览器不支持录音功能，请使用现代浏览器
                            </p>
                            <p v-else>
                                无法访问录音设备。可能原因：麦克风被其他应用占用、音频驱动异常或浏览器兼容性问题
                            </p>
                        </div>
                        <div class="error-actions">
                            <a-button type="primary" shape="round" size="middle" @click="checkMicrophone"
                                :loading="checkingMicrophone">
                                重新检测
                            </a-button>
                        </div>
                        <div class="error-tips">
                            <h4>解决方案：</h4>
                            <ul>
                                <li>确保麦克风已正确连接</li>
                                <li>检查浏览器麦克风权限设置</li>
                                <li>关闭其他正在使用麦克风的应用程序</li>
                                <li>尝试刷新页面重新授权</li>
                                <li>如果使用Edge浏览器，尝试切换到Chrome浏览器</li>
                                <li>重启浏览器或重启电脑</li>
                                <li>使用手机版应用进行声音克隆</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 正常录音界面 -->
                <template v-else>
                    <!-- 步骤指示器 -->
                    <div class="step-indicator">
                        <div class="step-list">
                            <div :class="['step-item', { 'active': currentStep === 1, 'completed': currentStep > 1 }]">
                                <div class="step-number">1</div>
                                <div class="step-label">选择语言</div>
                            </div>
                            <div class="step-line" :class="{ 'completed': currentStep > 1 }"></div>
                            <div :class="['step-item', { 'active': currentStep === 2, 'completed': currentStep > 2 }]">
                                <div class="step-number">2</div>
                                <div class="step-label">录制音频</div>
                            </div>
                            <div class="step-line" :class="{ 'completed': currentStep > 2 }"></div>
                            <div :class="['step-item', { 'active': currentStep === 3, 'completed': currentStep > 3 }]">
                                <div class="step-number">3</div>
                                <div class="step-label">完成复刻</div>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤内容区域 -->
                    <div class="step-content">
                        <!-- 步骤1: 语言选择 -->
                        <div v-if="currentStep === 1" class="step-panel step-prepare">
                            <!-- 语言选择 -->
                            <div class="clone-lang">
                                <div class="lang-content">
                                    <button :class="['lang-btn', 'lang-zh', { 'active': currentLang === 'zh' }]"
                                        @click.stop="changeLang('zh')">
                                        中文
                                    </button>
                                    <button :class="['lang-btn', 'lang-ug', { 'active': currentLang === 'ug' }]"
                                        @click.stop="changeLang('ug')" dir="rtl">
                                        ئۇيغۇرچە
                                    </button>
                                    <div :class="['lang-active', { 'ug-active': currentLang === 'ug' }]"></div>
                                </div>
                            </div>

                            <!-- 控制按钮 -->
                            <div class="step-actions">
                                <a-button shape="round" type="primary" size="large" @click="goToStep2">
                                    下一步
                                </a-button>
                            </div>
                        </div>

                        <!-- 步骤2: 录制音频 -->
                        <div v-if="currentStep === 2" class="step-panel step-recording">
                            <!-- 语言选择（只读显示） -->
                            <div class="current-lang">
                                <span class="lang-label">当前语言：</span>
                                <span class="lang-value" :dir="currentLang === 'ug' ? 'rtl' : 'ltr'">{{ currentLang ===
                                    'zh' ? '中文' : 'ئۇيغۇرچە' }}</span>
                            </div>

                            <!-- 参考文本（只读显示） -->
                            <div class="reference-section">
                                <div class="section-title">参考文本</div>
                                <div class="reference-text readonly" :dir="currentLang === 'ug' ? 'rtl' : 'ltr'">
                                    {{ referenceText }}
                                </div>
                            </div>

                            <div class="recorder-wave">
                                <!-- 准备录音提示 -->
                                <div class="recording-tips">
                                    推荐录制10-30s音频，避免多人对话、明显杂音、噪音、混响等情况
                                </div>
                                <!-- 波形显示 -->
                                <div class="wave-container">
                                    <div class="line-list">
                                        <div class="line-item" v-for="(level, index) in audioLevels" :key="index"
                                            :style="{ height: Math.max(4, level) + 'px' }"></div>
                                    </div>
                                </div>
                                <div class="time">{{ formatTime(recordingTime) }}</div>
                            </div>

                            <!-- 控制按钮 -->
                            <div class="step-actions">
                                <a-button shape="round" size="large" @click="goBackToPrepare" :disabled="isRecording">
                                    上一步
                                </a-button>

                                <a-button v-if="!isRecording" shape="round" type="primary" size="large"
                                    @click="startRecording">
                                    开始录音
                                </a-button>

                                <a-button v-if="isRecording" shape="round" type="primary" size="large"
                                    @click="stopRecording()">
                                    结束录音
                                </a-button>
                            </div>
                        </div>

                        <!-- 步骤3: 完成复刻 -->
                        <div v-if="currentStep === 3" class="step-panel step-complete">
                            <!-- 音频播放器 -->
                            <div class="audio-preview">
                                <div class="section-title">录音试听</div>
                                <div class="trial-listening">
                                    <AudioPlayer progress-type="wave" :audioUrl="audioUrl" ref="audioPlayerRef"
                                        :download="false" :restart="false" />
                                </div>
                            </div>
                            <!-- 名称输入 -->
                            <div class="name-form">
                                <div class="section-title">音色名称</div>
                                <a-form :model="formState" ref="formRef">
                                    <a-form-item name="newName" :rules="nameRules">
                                        <a-input v-model:value="formState.newName" placeholder="请输入音色名称" autofocus />
                                    </a-form-item>
                                </a-form>
                            </div>

                            <!-- 控制按钮 -->
                            <div class="step-actions">
                                <a-button shape="round" size="large" @click="restRecord">
                                    重新录音
                                </a-button>
                                <a-button shape="round" type="primary" size="large" @click="startClone">
                                    开始复刻
                                </a-button>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>


    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { message, Button as aButton, Form as aForm, Input as aInput, FormItem as aFormItem } from 'ant-design-vue';
import { CloseOutlined } from '@ant-design/icons-vue'
import { textToSpeechSerApi } from '../server/server'
import AudioPlayer from './AudioPlayer.vue';
import {
    AUDIO_CONFIG,
    convertToWav,
    disableBodyScroll,
    enableBodyScroll,
    generateStaticWaveHeights,
    getBrowserInfo,
    getAudioConstraints,
} from '../utils/utils';

const textToSpeechApi = textToSpeechSerApi()
const emit = defineEmits(['close'])

// 组件状态
const isClosing = ref(false)
const currentLang = ref<string>('zh')
const referenceText = ref<string>('')
const currentStep = ref<number>(1) // 1: 准备录音, 2: 录制音频, 3: 完成复刻

// 表单相关
const formState = ref({ newName: '' })
const formRef = ref()
const nameRules = [
    { required: true, message: '请输入音色名称！' },
    { max: 20, message: '名称不能超过20字符！' },
]
// 录音相关状态
const isRecording = ref(false)
const recordingTime = ref(0)
const audioLevels = ref<number[]>(new Array(100).fill(0))
const mediaRecorder = ref<MediaRecorder | null>(null)
const audioContext = ref<AudioContext | null>(null)
const analyser = ref<AnalyserNode | null>(null)
const animationId = ref<number | null>(null)
const recordingTimer = ref<number | null>(null)
const audioChunks = ref<Blob[]>([])
const audioUrl = ref<string>('')
const audioBlob = ref<Blob | null>(null)
const audioPlayerRef = ref<InstanceType<typeof AudioPlayer> | null>(null)

// 音频格式支持检查
const supportedFormats = ref<string[]>([])
const isWavSupported = ref(false)
const fallbackFormat = ref<string>('')

// 麦克风检测相关
const microphoneAvailable = ref(true)
const microphoneError = ref<'permission' | 'notfound' | 'notsupported' | 'unknown'>('unknown')
const checkingMicrophone = ref(false)

// 常量定义已移至 utils.ts

// 页面滚动控制方法已移至 utils.ts

// 检查浏览器支持的音频格式
const checkAudioFormats = () => {
    // console.log('=== 浏览器音频格式支持检查 ===')

    // 常见的音频格式列表
    const audioFormats = [
        'audio/wav',
        'audio/webm',
        'audio/webm;codecs=opus',
        'audio/ogg',
        'audio/ogg;codecs=opus',
        'audio/mp4',
        'audio/mpeg',
        'audio/3gpp',
        'audio/3gpp2'
    ]

    const supported: string[] = []

    audioFormats.forEach(format => {
        const isSupported = MediaRecorder.isTypeSupported(format)
        // console.log(`${format}: ${isSupported ? '✅ 支持' : '❌ 不支持'}`)
        if (isSupported) {
            supported.push(format)
        }
    })

    supportedFormats.value = supported
    isWavSupported.value = MediaRecorder.isTypeSupported('audio/wav')

    console.log('支持的格式列表:', supported)
    // console.log('WAV格式支持:', isWavSupported.value ? '✅ 支持' : '❌ 不支持')

    // 如果不支持WAV，选择备用格式
    if (!isWavSupported.value) {
        if (supported.includes('audio/webm;codecs=opus')) {
            fallbackFormat.value = 'audio/webm;codecs=opus'
        } else if (supported.includes('audio/webm')) {
            fallbackFormat.value = 'audio/webm'
        } else if (supported.includes('audio/ogg;codecs=opus')) {
            fallbackFormat.value = 'audio/ogg;codecs=opus'
        } else if (supported.includes('audio/ogg')) {
            fallbackFormat.value = 'audio/ogg'
        } else if (supported.length > 0) {
            fallbackFormat.value = supported[0]
        }

        // console.log('备用录音格式:', fallbackFormat.value)
        // console.log('将在录音后转换为WAV格式（单通道，采样率24000Hz）')
    }

    // console.log('=== 格式检查完成 ===')
}

// 音频格式转换函数已移至 utils.ts

onMounted(async () => {
    getReferenceText()
    // 初始化静态声浪效果
    initStaticWave()
    // 禁止页面滚动
    disableBodyScroll()
    // 检测麦克风可用性
    await checkMicrophone()
    // 检查浏览器支持的音频格式
    checkAudioFormats()
})

// 初始化静态声浪效果
const initStaticWave = () => {
    audioLevels.value = generateStaticWaveHeights(AUDIO_CONFIG.WAVE_BARS, AUDIO_CONFIG.MIN_HEIGHT, 20)
}

onUnmounted(() => {
    // 清理资源
    stopRecording(false)
    if (audioContext.value) {
        audioContext.value.close()
    }
    if (animationId.value) {
        cancelAnimationFrame(animationId.value)
    }
    if (recordingTimer.value) {
        clearInterval(recordingTimer.value)
    }
    // 恢复页面滚动
    enableBodyScroll()
})

const startClone = async () => {

    if (!audioBlob.value) {
        message.error('请先录音')
        return
    }

    try {
        await formRef.value.validateFields();
    } catch (err) {
        message.error('请输入音色名称')
        return
    }
    const hide = message.loading('正在复刻中....', 0);
    try {
        // 严格使用WAV格式和扩展名
        const audioFile = new File([audioBlob.value], `voice_clone_${Date.now()}.wav`, {
            type: 'audio/wav'
        })
        await textToSpeechApi.upload_voice({ name: formState.value.newName, audio_file: audioFile })
        // 如果成功，可以在这里添加成功提示
        message.success('复刻成功')
        emit('close', 'success')
    } catch (error) {
        console.error('声音复刻失败:', error)
        message.error('声音复刻失败，请重试')
    } finally {
        hide() // 确保在任何情况下都会关闭loading
    }
}
// 步骤切换方法
const goBackToPrepare = () => {
    if (isRecording.value) {
        stopRecording(false)
    }
    currentStep.value = 1
}

const goToComplete = () => {
    currentStep.value = 3
}

// 从第一步跳转到第二步
const goToStep2 = () => {
    currentStep.value = 2
}

// 麦克风检测方法
const checkMicrophone = async () => {
    checkingMicrophone.value = true

    try {
        // 检查浏览器是否支持 getUserMedia
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            microphoneError.value = 'notsupported'
            microphoneAvailable.value = false
            return
        }

        // 尝试获取音频设备列表
        const devices = await navigator.mediaDevices.enumerateDevices()
        const audioInputDevices = devices.filter(device => device.kind === 'audioinput')

        if (audioInputDevices.length === 0) {
            microphoneError.value = 'notfound'
            microphoneAvailable.value = false
            return
        }

        // 获取适合当前浏览器的音频约束
        const audioConstraints = getAudioConstraints()

        let stream: MediaStream | null = null
        let lastError: any = null

        // 逐个尝试不同的约束
        for (const constraints of audioConstraints) {
            try {
                stream = await navigator.mediaDevices.getUserMedia(constraints)
                console.log('麦克风检测成功，使用约束:', constraints)
                break
            } catch (error: any) {
                lastError = error
                console.warn('尝试约束失败:', constraints, error.name)

                // 如果是权限问题，直接抛出，不再尝试其他约束
                if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
                    throw error
                }

                // 继续尝试下一个约束
                continue
            }
        }

        // 如果所有约束都失败了
        if (!stream) {
            throw lastError || new Error('无法获取音频流')
        }

        // 成功获取权限，立即停止流
        stream.getTracks().forEach(track => track.stop())

        // 设置为可用状态
        microphoneAvailable.value = true
        // message.success('麦克风检测成功')

    } catch (error: any) {
        const browserInfo = getBrowserInfo()
        console.error('麦克风检测失败:', error, '浏览器:', browserInfo.name)

        // 详细的错误分类
        if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
            microphoneError.value = 'permission'
        } else if (error.name === 'NotFoundError' || error.name === 'DevicesNotFoundError') {
            microphoneError.value = 'notfound'
        } else if (error.name === 'NotSupportedError') {
            microphoneError.value = 'notsupported'
        } else if (error.name === 'NotReadableError') {
            // 专门处理 NotReadableError，特别是在Edge浏览器中
            if (browserInfo.isEdge) {
                console.warn('Edge浏览器中的NotReadableError，可能是音频设备被占用或驱动兼容性问题')
            } else {
                console.warn('音频设备可能被其他应用占用或驱动异常')
            }
            microphoneError.value = 'unknown'
        } else {
            microphoneError.value = 'unknown'
        }

        microphoneAvailable.value = false
    } finally {
        checkingMicrophone.value = false
    }
}


const changeLang = (lang: string) => {
    if (currentLang.value === lang) return
    if (!isRecording.value) {
        currentLang.value = lang
        getReferenceText()
    } else {
        message.error('请先结束录音')
    }
}

const onClose = () => {
    // console.log('关闭')
    isClosing.value = true
    if (audioPlayerRef.value) {
        audioPlayerRef.value.pauseAudio()
    }
    // 恢复页面滚动
    enableBodyScroll()
    // 等待出场动画完成后再触发关闭事件
    setTimeout(() => {
        emit('close')
    }, 200) // 300ms 对应动画时长
}

const getReferenceText = async () => {
    try {
        const res: any = await textToSpeechApi.get_reference_text(currentLang.value)
        referenceText.value = res.reference_text
    } catch (error) {
        message.error('获取参考文本失败，请稍后再试！')
    }
}

// 格式化时间显示
const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 开始录音
const startRecording = async () => {
    try {
        // 获取适合当前浏览器的音频约束
        const audioConstraints = getAudioConstraints()

        let stream: MediaStream | null = null

        for (const constraints of audioConstraints) {
            try {
                stream = await navigator.mediaDevices.getUserMedia(constraints)
                console.log('录音开始成功，使用约束:', constraints)
                break
            } catch (error: any) {
                console.warn('录音约束尝试失败:', constraints, error.name)
                if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
                    throw error // 权限问题直接抛出
                }
                continue
            }
        }

        if (!stream) {
            throw new Error('无法获取音频流')
        }

        // 创建音频上下文和分析器
        audioContext.value = new AudioContext({ sampleRate: AUDIO_CONFIG.SAMPLE_RATE })
        analyser.value = audioContext.value.createAnalyser()
        const source = audioContext.value.createMediaStreamSource(stream)
        source.connect(analyser.value)

        analyser.value.fftSize = 256
        const bufferLength = analyser.value.frequencyBinCount
        const dataArray = new Uint8Array(bufferLength)

        // 根据浏览器支持情况选择录音格式
        let recordingFormat = 'audio/wav'
        let options: MediaRecorderOptions = {
            mimeType: 'audio/wav',
            audioBitsPerSecond: AUDIO_CONFIG.BIT_RATE
        }

        if (!isWavSupported.value) {
            if (!fallbackFormat.value) {
                message.error('您的浏览器不支持任何可用的音频录制格式')
                stream.getTracks().forEach(track => track.stop())
                return
            }

            recordingFormat = fallbackFormat.value
            options = {
                mimeType: fallbackFormat.value,
                audioBitsPerSecond: AUDIO_CONFIG.BIT_RATE
            }

            // console.log(`使用备用格式录音: ${fallbackFormat.value}，录音后将转换为WAV格式`)
        } else {
            // console.log('使用WAV格式直接录音')
        }

        mediaRecorder.value = new MediaRecorder(stream, options)
        audioChunks.value = []

        mediaRecorder.value.ondataavailable = (event) => {
            if (event.data.size > 0) {
                audioChunks.value.push(event.data)
            }
        }

        mediaRecorder.value.onstop = async () => {
            const recordedBlob = new Blob(audioChunks.value, { type: recordingFormat })
            console.log('原始录音完成:', {
                格式: recordingFormat,
                大小: recordedBlob.size,
                需要转换: !isWavSupported.value
            })

            try {
                let finalBlob = recordedBlob

                // 如果不是WAV格式，需要转换
                if (!isWavSupported.value) {
                    // console.log('开始转换音频格式...')
                    finalBlob = await convertToWav(recordedBlob)
                    // console.log('音频格式转换完成')
                }

                audioBlob.value = finalBlob // 保存最终的WAV格式Blob对象
                audioUrl.value = URL.createObjectURL(finalBlob)
                console.log('最终音频信息:', {
                    格式: 'WAV',
                    大小: finalBlob.size,
                    采样率: '24000Hz',
                    声道: '单声道'
                })

                // 录音完成后自动跳转到第3步
                goToComplete()
            } catch (error) {
                console.error('音频处理失败:', error)
                message.error('音频处理失败，请重试')
            }
        }

        // 开始录音
        mediaRecorder.value.start()
        isRecording.value = true
        recordingTime.value = 0

        // 开始计时
        recordingTimer.value = setInterval(() => {
            recordingTime.value++
        }, 1000)

        // 开始音频分析和声浪动画
        const updateAudioLevels = () => {
            if (!isRecording.value || !analyser.value) return

            analyser.value.getByteFrequencyData(dataArray)

            // 计算音频级别并更新声浪
            const newLevels = [...audioLevels.value]

            // 计算多个频段的平均值，让声浪更自然
            const lowFreq = dataArray.slice(0, 10).reduce((sum, value) => sum + value, 0) / 10
            const midFreq = dataArray.slice(10, 30).reduce((sum, value) => sum + value, 0) / 20
            const highFreq = dataArray.slice(30, 60).reduce((sum, value) => sum + value, 0) / 30

            // 综合不同频段，创建更丰富的声浪效果
            const combinedLevel = (lowFreq * 0.6 + midFreq * 0.3 + highFreq * 0.1)
            const normalizedLevel = Math.min(AUDIO_CONFIG.MAX_HEIGHT, Math.max(AUDIO_CONFIG.MIN_HEIGHT, (combinedLevel / 255) * AUDIO_CONFIG.MAX_HEIGHT))

            // 添加一些随机变化让声浪更自然
            const randomVariation = (Math.random() - 0.5) * 8
            const finalLevel = Math.max(AUDIO_CONFIG.MIN_HEIGHT, Math.min(AUDIO_CONFIG.MAX_HEIGHT, normalizedLevel + randomVariation))

            // 将新的音频数据添加到数组开头，移除末尾的数据
            newLevels.unshift(finalLevel)
            newLevels.pop()

            audioLevels.value = newLevels

            animationId.value = requestAnimationFrame(updateAudioLevels)
        }

        updateAudioLevels()
        message.success('开始录音')

    } catch (error) {
        console.error('录音失败:', error)
        message.error('无法访问麦克风，请检查权限设置')
    }
}

// 停止录音
const stopRecording = (msg: boolean | undefined = true) => {
    if (mediaRecorder.value && isRecording.value) {
        mediaRecorder.value.stop()

        // 停止所有音频轨道
        if (mediaRecorder.value.stream) {
            mediaRecorder.value.stream.getTracks().forEach(track => track.stop())
        }
    }

    isRecording.value = false

    if (recordingTimer.value) {
        clearInterval(recordingTimer.value)
        recordingTimer.value = null
    }

    if (animationId.value) {
        cancelAnimationFrame(animationId.value)
        animationId.value = null
    }

    // 恢复静态声浪
    initStaticWave()

    if (msg) {
        message.success('录音结束')
    }
}
// 重置录音
const restRecord = () => {
    stopRecording(false)
    audioUrl.value = ''
    audioBlob.value = null
    audioChunks.value = []
    recordingTime.value = 0
    formState.value.newName = ''
    currentStep.value = 1
}

</script>

<style lang="scss">
:root {
    --clone-bg: rgba(0, 0, 0, 0.5);
    --content-bg: #ffffff;
}

:root.dark {
    --clone-bg: rgba(0, 0, 0, 0.7);
    --content-bg: #1f1f1f;
}

.clone-com {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: var(--clone-bg);
    z-index: 1000;
    color: var(--text-color);
    padding: 20px;
    box-sizing: border-box;

    .dialog-ctrl {
        width: min(600px, 90vw);
        max-height: 90vh;
        position: absolute;
        top: 100px;
        left: calc((100vw - 600px) / 2);
        background: var(--content-bg);
        border-radius: 16px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        animation: slideIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        overflow: hidden;
        display: flex;
        flex-direction: column;

        &.closing {
            animation: slideOut 0.2s ease-in;
        }

        .dlg-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            flex-shrink: 0;

            .dlg-title {
                font-size: 18px;
                font-weight: 600;
                margin: 0;
                color: var(--text-color);
            }

            .dlg-close-btn {
                background: none;
                border: none;
                cursor: pointer;
                width: 32px;
                height: 32px;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 8px;
                color: var(--text-color);
                transition: background-color 0.2s;

                &:hover {
                    background: rgba(0, 0, 0, 0.05);
                }
            }
        }

        .dlg-content {
            padding: 24px;
            flex: 1;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 24px;

            // 步骤指示器
            .step-indicator {
                margin-bottom: 32px;

                .step-list {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0;

                    .step-item {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        gap: 8px;
                        position: relative;

                        .step-number {
                            width: 36px;
                            height: 36px;
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 14px;
                            font-weight: 600;
                            background: var(--border-color, #d9d9d9);
                            color: var(--text-color-secondary, #666);
                            transition: all 0.3s ease;
                        }

                        .step-label {
                            font-size: 12px;
                            color: var(--text-color-secondary, #666);
                            white-space: nowrap;
                            transition: color 0.3s ease;
                        }

                        &.active {
                            .step-number {
                                background: var(--primary-color);
                                color: white;
                                transform: scale(1.1);
                            }

                            .step-label {
                                color: var(--primary-color);
                                font-weight: 500;
                            }
                        }

                        &.completed {
                            .step-number {
                                background: var(--success-color, #52c41a);
                                color: white;
                            }

                            .step-label {
                                color: var(--success-color, #52c41a);
                            }
                        }
                    }

                    .step-line {
                        width: 80px;
                        height: 2px;
                        background: var(--border-color, #d9d9d9);
                        transition: background-color 0.3s ease;

                        &.completed {
                            background: var(--success-color, #52c41a);
                        }
                    }
                }
            }

            // 麦克风错误界面
            .microphone-error {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 60px 40px;
                text-align: center;
                min-height: 400px;

                .error-icon {
                    margin-bottom: 24px;
                    color: var(--error-color, #ff4d4f);
                    opacity: 0.8;
                }

                .error-content {
                    max-width: 480px;

                    .error-title {
                        font-size: 24px;
                        font-weight: 600;
                        color: var(--text-color);
                        margin-bottom: 16px;
                    }

                    .error-message {
                        font-size: 16px;
                        color: var(--text-color-secondary, #666);
                        line-height: 1.6;
                        margin-bottom: 32px;

                        p {
                            margin: 0;
                        }
                    }

                    .error-actions {
                        display: flex;
                        justify-content: center;
                        gap: 16px;
                        margin-bottom: 32px;

                        .ant-btn {
                            min-width: 140px;
                            height: 44px;
                            font-size: 14px;
                            font-weight: 500;
                        }
                    }

                    .error-tips {
                        background: var(--textarea-bg, #fafafa);
                        border-radius: 12px;
                        padding: 20px;
                        border: 1px solid var(--border-color);
                        text-align: left;

                        h4 {
                            font-size: 14px;
                            font-weight: 600;
                            color: var(--text-color);
                            margin: 0 0 12px 0;
                        }

                        ul {
                            margin: 0;
                            padding-left: 20px;
                            color: var(--text-color-secondary, #666);
                            font-size: 14px;
                            line-height: 1.6;

                            li {
                                margin-bottom: 8px;

                                &:last-child {
                                    margin-bottom: 0;
                                }
                            }
                        }
                    }
                }
            }

            // 步骤内容区域
            .step-content {
                flex: 1;
                display: flex;
                flex-direction: column;

                .step-panel {
                    display: flex;
                    flex-direction: column;
                    gap: 24px;
                    animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);

                    .section-title {
                        font-size: 16px;
                        font-weight: 600;
                        color: var(--text-color);
                        margin-bottom: 12px;
                    }

                    .step-actions {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        gap: 16px;
                        margin-top: auto;
                        padding-top: 24px;

                        .ant-btn {
                            min-width: 120px;
                            height: 44px;
                            font-size: 14px;
                            font-weight: 500;
                        }
                    }
                }

                // 步骤1样式
                .step-prepare {
                    .current-lang {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 8px;
                        font-size: 14px;
                        color: var(--text-color);
                        margin-bottom: 16px;

                        .lang-label {
                            color: var(--text-color-secondary, #666);
                        }

                        .lang-value {
                            font-weight: 500;
                            color: var(--primary-color);
                        }
                    }
                }

                // 步骤2样式
                .step-recording {
                    .current-lang {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 8px;
                        font-size: 14px;
                        color: var(--text-color);
                        margin-bottom: 16px;

                        .lang-label {
                            color: var(--text-color-secondary, #666);
                        }

                        .lang-value {
                            font-weight: 500;
                            color: var(--primary-color);
                        }
                    }

                    .reference-text.readonly {
                        opacity: 0.8;
                        pointer-events: none;
                    }
                }

                // 步骤3样式
                .step-complete {
                    .audio-preview {
                        .trial-listening {
                            // min-height: 80px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            padding: 0px 15px;
                        }
                    }
                }
            }

            // 语言选择器
            .clone-lang {
                .lang-content {
                    width: 220px;
                    height: 48px;
                    background: var(--textarea-bg, #f5f5f5);
                    margin: 0 auto;
                    border-radius: 24px;
                    position: relative;
                    padding: 4px;

                    .lang-btn {
                        width: 100px;
                        height: 40px;
                        position: absolute;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border-radius: 20px;
                        z-index: 2;
                        cursor: pointer;
                        background: none;
                        border: none;
                        font-size: 14px;
                        font-weight: 500;
                        transition: color 0.3s ease;
                        color: var(--text-color);

                        &.lang-zh {
                            left: 4px;
                        }

                        &.lang-ug {
                            right: 4px;
                        }

                        &.active {
                            color: var(--primary-color);
                        }
                    }

                    .lang-active {
                        width: 100px;
                        height: 40px;
                        background: white;
                        position: absolute;
                        top: 4px;
                        left: 4px;
                        border-radius: 20px;
                        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                        z-index: 1;
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

                        &.ug-active {
                            left: 116px;
                        }
                    }
                }
            }

            // 参考文本
            .reference-section {
                // 添加过渡动画
                transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
                transform-origin: center top;

                .reference-text {
                    min-height: 100px;
                    border-radius: 12px;
                    border: 1px solid var(--border-color);
                    padding: 16px;
                    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
                    font-size: 14px;
                    color: var(--text-color);
                    line-height: 1.6;
                    background: var(--textarea-bg, #fafafa);
                    overflow-y: auto;

                    &::-webkit-scrollbar {
                        width: 6px;
                    }

                    &::-webkit-scrollbar-track {
                        background: transparent;
                    }

                    &::-webkit-scrollbar-thumb {
                        background: var(--border-color);
                        border-radius: 3px;
                    }
                }
            }

            // 录音波形区域
            .recorder-wave {
                // background: var(--textarea-bg, #fafafa);
                // border-radius: 12px;
                padding: 20px;
                // border: 1px solid var(--border-color);

                .recording-tips {
                    text-align: center;
                    font-size: 12px;
                    color: var(--text-color-secondary, #666);
                    margin-bottom: 16px;
                    line-height: 1.4;
                }



                .wave-container {
                    margin: 16px 0;
                }

                .line-list {
                    display: flex;
                    align-items: center;
                    height: 80px;
                    justify-content: center;
                    overflow: hidden;
                    gap: 1px;

                    .line-item {
                        width: 3px;
                        min-height: 4px;
                        max-height: 80px;
                        background: var(--primary-color);
                        border-radius: 2px;
                        transition: height 0.15s ease-out;
                        opacity: 0.8;

                        &:nth-child(odd) {
                            opacity: 0.9;
                        }

                        &:nth-child(even) {
                            opacity: 0.7;
                        }
                    }
                }

                .time {
                    font-size: 14px;
                    color: var(--text-color-secondary, #666);
                    text-align: center;
                    margin-top: 12px;
                    font-weight: 500;
                }
            }

            // 名称表单
            .name-form {
                .ant-form {
                    width: 90%;
                    margin: 0 auto;


                }

                .ant-input {
                    text-align: center;
                    border-radius: 8px;
                    padding: 12px 16px;
                    font-size: 14px;
                }
            }
        }
    }

    // 响应式设计
    @media screen and (max-width: 768px) {
        padding: 16px;

        .dialog-ctrl {
            width: 90vw;
            max-height: 90vh;
            position: absolute;
            top: 60px;
            left: calc((100vw - 90vw) / 2);
            max-height: 95vh;
            border-radius: 12px;

            .dlg-header {
                padding: 10px 15px;

                .dlg-title {
                    font-size: 16px;
                }
            }
            .dlg-content {
                padding: 20px;
                gap: 20px;

                .step-indicator .step-list {
                    .step-item .step-label {
                        font-size: 11px;
                    }

                    .step-line {
                        width: 60px;
                    }
                }

                .clone-lang .lang-content {
                    width: 200px;
                    height: 44px;

                    .lang-btn {
                        width: 90px;
                        height: 36px;
                        font-size: 13px;
                    }

                    .lang-active {
                        width: 90px;
                        height: 36px;

                        &.ug-active {
                            left: 106px;
                        }
                    }
                }

                .step-content .step-panel .step-actions {
                    gap: 12px;

                    .ant-btn {
                        min-width: 100px;
                        height: 40px;
                        font-size: 13px;
                    }
                }
            }
        }
    }

    @media screen and (max-width: 480px) {
        .dialog-ctrl .dlg-content {
            .step-indicator .step-list {
                .step-item .step-number {
                    width: 32px;
                    height: 32px;
                    font-size: 12px;
                }

                .step-line {
                    width: 40px;
                }
            }

            .step-content .step-panel .step-actions {
                flex-direction: column;
                gap: 12px;

                .ant-btn {
                    width: 100%;
                    max-width: 200px;
                }
            }
        }
    }

    // 动画效果
    @keyframes slideIn {
        from {
            transform: scale(0.9) translateY(-20px);
            opacity: 0;
        }

        to {
            transform: scale(1) translateY(0);
            opacity: 1;
        }
    }

    @keyframes slideOut {
        from {
            transform: scale(1) translateY(0);
            opacity: 1;
        }

        to {
            transform: scale(0.9) translateY(-20px);
            opacity: 0;
        }
    }

    @keyframes fadeInUp {
        from {
            transform: translateY(30px);
            opacity: 0;
        }

        50% {
            opacity: 0.7;
        }

        to {
            transform: translateY(0);
            opacity: 1;
        }
    }


}
</style>