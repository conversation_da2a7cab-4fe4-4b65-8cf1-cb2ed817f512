// ==================== 语言检测相关 ====================

/**
 * 检测文本语言类型
 * @param inputText 输入文本
 * @returns 语言检测结果
 */
export const detectLanguage = (inputText: string | undefined) => {
    if (!inputText) return { lang: '', text: '', counts: { zh: 0, ug: 0, en: 0, other: 0 }, dir: 'ltr' };

    // 中文Unicode范围
    const chineseRanges = [
        [0x4E00, 0x9FFF],   // 基本汉字
        [0x3400, 0x4DBF],   // 扩展A
        [0x20000, 0x2A6DF], // 扩展B
    ];

    // 维吾尔语（阿拉伯字母）Unicode范围
    const uyghurRanges = [
        [0x0600, 0x06FF],   // 基本阿拉伯字母
        [0x0750, 0x077F],   // 阿拉伯字母补充
        [0x08A0, 0x08FF],   // 阿拉伯字母扩展-A
    ];

    // 英语（拉丁字母）Unicode范围
    const englishRanges = [
        [0x0041, 0x005A], // A-Z
        [0x0061, 0x007A], // a-z
    ];

    let zh = 0, ug = 0, en = 0, other = 0;

    for (const char of inputText) {
        const code: number = char.codePointAt(0) || 0;
        let matched = false;

        // 检查中文
        for (const [start, end] of chineseRanges) {
            if (code >= start && code <= end) {
                zh++;
                matched = true;
                break;
            }
        }
        if (matched) continue;

        // 检查维吾尔语
        for (const [start, end] of uyghurRanges) {
            if (code >= start && code <= end) {
                ug++;
                matched = true;
                break;
            }
        }
        if (matched) continue;

        // 检查英语
        for (const [start, end] of englishRanges) {
            if (code >= start && code <= end) {
                en++;
                matched = true;
                break;
            }
        }
        if (matched) continue;

        // 忽略空格
        if (char.trim() !== '') {
            other++;
        }
    }

    // 构建语言代码
    const langParts = [];
    if (zh > 0) langParts.push('zh');
    if (ug > 0) langParts.push('ug');
    if (en > 0) langParts.push('en');
    const langCode = langParts.join('+') || 'other';

    // 构建描述文本
    let description = '';
    if (langParts.length === 1) {
        description =
            langParts[0] === 'zh' ? '中文' :
            langParts[0] === 'ug' ? '维吾尔文' :
            '英文';
    } else if (langParts.length > 1) {
        const parts = [];
        if (zh > 0) parts.push('中文');
        if (ug > 0) parts.push('维吾尔文');
        if (en > 0) parts.push('英文');
        description = parts.join('和');
    } else {
        description = '其他语言';
    }

    let dir = 'ltr';
    if (ug > 0) {
        if (ug > en && ug > zh) {
            dir = 'rtl';
        } else if (en === 0 && zh === 0) {
            dir = 'rtl';
        }
    }

    return {
        lang: langCode,
        text: description,
        dir,
        counts: { zh, ug, en, other }
    };
}

/**
 * 获取文本方向
 * @param text 文本内容
 * @returns 文本方向 'ltr' | 'rtl'
 */
export const textDir = (text: any): 'ltr' | 'rtl' => {
    const { dir } = detectLanguage(text);
    return dir as 'ltr' | 'rtl';
}

// ==================== 音频处理相关 ====================

/**
 * 音频配置常量
 */
export const AUDIO_CONFIG = {
    // 录音配置
    SAMPLE_RATE: 24000, // 采样率，每秒采样点数，通常为44100或48000，表示每秒44100或48000个采样点
    CHANNELS: 1, // 通道数，通常为1（单声道）或2（立体声）
    BIT_RATE: 384000, // 位率，表示每秒传输的比特数，通常为128000、192000、256000等
    WAVE_BARS: 100, // 波形条数，表示音频波形的条数，通常为100或200等
    MIN_HEIGHT: 4, // 最小高度，表示音频波形的最小高度，通常为4或8等
    MAX_HEIGHT: 80, // 最大高度，表示音频波形的最大高度，通常为80或120等

    // 音频分析配置
    FFT_SIZE: 256, // 快速傅里叶变换的大小，通常为256、512、1024等，表示将音频数据分割为多少个频率段进行分析
    SMOOTHING_TIME_CONSTANT: 0.8, // 平滑时间常数，表示音频波形的平滑程度，通常为0.8或1.0等
    BASE_HEIGHT: 8, // 基础高度，表示音频波形的基础高度，通常为8或16等
    MAX_ADDITIONAL_HEIGHT: 32 // 最大额外高度，表示音频波形的最大额外高度，通常为32或64等
} as const;

/**
 * 音频格式支持检查
 * @returns 支持的音频格式列表
 */
export const checkAudioFormatSupport = (): {
    supportedFormats: string[];
    isWavSupported: boolean;
    fallbackFormat: string;
} => {
    const formats = [
        'audio/wav',
        'audio/webm;codecs=opus',
        'audio/webm',
        'audio/mp4',
        'audio/ogg;codecs=opus',
        'audio/ogg'
    ];

    const supportedFormats = formats.filter(format => MediaRecorder.isTypeSupported(format));
    const isWavSupported = supportedFormats.includes('audio/wav');
    const fallbackFormat = supportedFormats.length > 0 ? supportedFormats[0] : '';

    return {
        supportedFormats,
        isWavSupported,
        fallbackFormat
    };
};

/**
 * 检测浏览器类型和版本
 * @returns 浏览器信息
 */
export const getBrowserInfo = () => {
    const userAgent = navigator.userAgent;
    const isEdge = /Edg/.test(userAgent);
    const isChrome = /Chrome/.test(userAgent) && !/Edg/.test(userAgent);
    const isFirefox = /Firefox/.test(userAgent);
    const isSafari = /Safari/.test(userAgent) && !/Chrome/.test(userAgent);

    return {
        isEdge,
        isChrome,
        isFirefox,
        isSafari,
        name: isEdge ? 'Edge' : isChrome ? 'Chrome' : isFirefox ? 'Firefox' : isSafari ? 'Safari' : 'Unknown',
        userAgent
    };
};

/**
 * 获取适合当前浏览器的音频约束配置
 * @param preferredSampleRate 首选采样率
 * @param preferredChannels 首选声道数
 * @returns 音频约束配置数组，按优先级排序
 */
export const getAudioConstraints = (
    preferredSampleRate: number = AUDIO_CONFIG.SAMPLE_RATE,
    preferredChannels: number = AUDIO_CONFIG.CHANNELS
): MediaStreamConstraints[] => {
    const browserInfo = getBrowserInfo();

    // Edge浏览器使用更保守的约束策略
    if (browserInfo.isEdge) {
        return [
            // Edge: 最基本的约束
            { audio: true },
            // Edge: 基本音频处理
            {
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                }
            },
            // Edge: 标准采样率
            {
                audio: {
                    sampleRate: 44100,
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                }
            }
        ];
    }

    // 其他浏览器使用完整的约束策略
    return [
        // 首选：完整配置
        {
            audio: {
                channelCount: preferredChannels,
                sampleRate: preferredSampleRate,
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true
            }
        },
        // 降级：标准采样率
        {
            audio: {
                channelCount: preferredChannels,
                sampleRate: 44100,
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true
            }
        },
        // 降级：移除采样率约束
        {
            audio: {
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true
            }
        },
        // 最后：最基本约束
        { audio: true }
    ];
};

/**
 * 将AudioBuffer转换为WAV格式的Blob
 * @param buffer AudioBuffer对象
 * @returns WAV格式的Blob
 */
export const audioBufferToWav = (buffer: AudioBuffer): Blob => {
    const length = buffer.length;
    const sampleRate = buffer.sampleRate;
    const channels = buffer.numberOfChannels;

    // WAV文件头部信息
    const arrayBuffer = new ArrayBuffer(44 + length * channels * 2);
    const view = new DataView(arrayBuffer);

    // WAV文件头
    const writeString = (offset: number, string: string) => {
        for (let i = 0; i < string.length; i++) {
            view.setUint8(offset + i, string.charCodeAt(i));
        }
    };

    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length * channels * 2, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, channels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * channels * 2, true);
    view.setUint16(32, channels * 2, true);
    view.setUint16(34, 16, true);
    writeString(36, 'data');
    view.setUint32(40, length * channels * 2, true);

    // 写入音频数据
    let offset = 44;
    for (let i = 0; i < length; i++) {
        for (let channel = 0; channel < channels; channel++) {
            const sample = Math.max(-1, Math.min(1, buffer.getChannelData(channel)[i]));
            view.setInt16(offset, sample < 0 ? sample * 0x8000 : sample * 0x7FFF, true);
            offset += 2;
        }
    }

    return new Blob([arrayBuffer], { type: 'audio/wav' });
};

/**
 * 将其他格式的音频转换为WAV格式
 * @param audioBlob 原始音频Blob
 * @returns 转换后的WAV格式Blob
 */
export const convertToWav = async (audioBlob: Blob): Promise<Blob> => {
    return new Promise((resolve, reject) => {
        const audioContext = new AudioContext({ sampleRate: AUDIO_CONFIG.SAMPLE_RATE });
        const fileReader = new FileReader();

        fileReader.onload = async (e) => {
            try {
                const arrayBuffer = e.target?.result as ArrayBuffer;
                const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

                // 确保是单通道
                const channels = AUDIO_CONFIG.CHANNELS;
                const sampleRate = AUDIO_CONFIG.SAMPLE_RATE;
                const length = Math.ceil(audioBuffer.duration * sampleRate);

                // 创建新的AudioBuffer用于重采样和转换为单通道
                const offlineContext = new OfflineAudioContext(channels, length, sampleRate);
                const source = offlineContext.createBufferSource();
                source.buffer = audioBuffer;
                source.connect(offlineContext.destination);
                source.start();

                const renderedBuffer = await offlineContext.startRendering();

                // 将AudioBuffer转换为WAV格式
                const wavBlob = audioBufferToWav(renderedBuffer);
                resolve(wavBlob);
            } catch (error) {
                reject(error);
            }
        };

        fileReader.onerror = () => reject(new Error('文件读取失败'));
        fileReader.readAsArrayBuffer(audioBlob);
    });
};

// ==================== 设备检测相关 ====================

/**
 * 麦克风错误类型
 */
export type MicrophoneErrorType = 'permission' | 'notfound' | 'notsupported' | 'unknown';

/**
 * 麦克风检测结果
 */
export interface MicrophoneCheckResult {
    available: boolean;
    error?: MicrophoneErrorType;
    message?: string;
}

/**
 * 检测麦克风可用性
 * @returns 麦克风检测结果
 */
export const checkMicrophoneAvailability = async (): Promise<MicrophoneCheckResult> => {
    try {
        // 检查浏览器是否支持 getUserMedia
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            return {
                available: false,
                error: 'notsupported',
                message: '您的浏览器不支持录音功能，请使用现代浏览器'
            };
        }

        // 尝试获取音频设备列表
        const devices = await navigator.mediaDevices.enumerateDevices();
        const audioInputDevices = devices.filter(device => device.kind === 'audioinput');

        if (audioInputDevices.length === 0) {
            return {
                available: false,
                error: 'notfound',
                message: '未检测到麦克风设备，请连接麦克风后重试'
            };
        }

        // 尝试获取麦克风权限
        const stream = await navigator.mediaDevices.getUserMedia({
            audio: {
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true
            }
        });

        // 成功获取权限，立即停止流
        stream.getTracks().forEach(track => track.stop());

        return {
            available: true,
            message: '麦克风检测成功'
        };

    } catch (error: any) {
        let errorType: MicrophoneErrorType = 'unknown';
        let message = '无法访问录音设备，请检查设备连接和权限设置';

        if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
            errorType = 'permission';
            message = '请允许浏览器访问您的麦克风权限，然后刷新页面重试';
        } else if (error.name === 'NotFoundError' || error.name === 'DevicesNotFoundError') {
            errorType = 'notfound';
            message = '未检测到麦克风设备，请连接麦克风后重试';
        } else if (error.name === 'NotSupportedError') {
            errorType = 'notsupported';
            message = '您的浏览器不支持录音功能，请使用现代浏览器';
        }

        return {
            available: false,
            error: errorType,
            message
        };
    }
};


// ==================== 时间和格式化相关 ====================

/**
 * 格式化时间为 mm:ss 格式
 * @param seconds 秒数
 * @returns 格式化后的时间字符串
 */
export const formatTime = (seconds: number): string => {
    if (!isFinite(seconds) || isNaN(seconds) || seconds < 0) {
        return '00:00';
    }

    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);

    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

/**
 * 下载文件（基础方法，可能受跨域限制影响）
 * @param url 文件URL
 * @param filename 文件名
 * @returns 是否可能成功（注意：这只是一个估计，不能保证真正成功）
 */
export const downloadFile = (url: string, filename: string = `audio-${Date.now()}.wav`): boolean => {
    try {
        // 检查是否是同源URL
        const currentOrigin = window.location.origin;
        const fileUrl = new URL(url, window.location.href);
        const isSameOrigin = fileUrl.origin === currentOrigin;

        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 如果是跨域URL，download属性可能会被忽略
        if (!isSameOrigin) {
            console.warn('跨域下载，文件名可能使用服务器原始名称');
            return false; // 表示可能没有使用自定义文件名
        }

        return true; // 表示可能成功使用了自定义文件名
    } catch (error) {
        console.error('downloadFile 失败:', error);
        throw error;
    }
};

/**
 * 智能下载文件（自动处理跨域问题）
 * @param url 文件URL
 * @param filename 自定义文件名
 * @param showMessage 是否显示提示信息
 * @returns Promise<boolean> 是否使用了自定义文件名
 */
export const smartDownloadFile = async (
    url: string,
    filename: string = `audio-${Date.now()}.wav`,
    showMessage: boolean = true
): Promise<boolean> => {
    try {
        // 检查是否是同源URL
        const currentOrigin = window.location.origin;
        const fileUrl = new URL(url, window.location.href);
        const isSameOrigin = fileUrl.origin === currentOrigin;

        if (isSameOrigin) {
            // 同源文件，可以使用自定义文件名
            downloadFile(url, filename);
            if (showMessage) {
                console.log(`✅ 下载成功，文件名: ${filename}`);
            }
            return true;
        } else {
            // 跨域文件，尝试通过 iframe 下载
            if (showMessage) {
                console.warn(`⚠️ 跨域下载限制，文件名可能是服务器原始名称而不是 "${filename}"`);
            }

            // 使用隐藏的 iframe 进行下载，这样不会打开新标签页
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            iframe.src = url;
            document.body.appendChild(iframe);

            // 5秒后移除 iframe
            setTimeout(() => {
                if (iframe.parentNode) {
                    iframe.parentNode.removeChild(iframe);
                }
            }, 5000);

            return false; // 表示可能没有使用自定义文件名
        }
    } catch (error) {
        console.error('smartDownloadFile 失败:', error);

        // 最后的备选方案：直接打开链接
        window.open(url, '_blank');
        return false;
    }
};

// ==================== 页面控制相关 ====================

/**
 * 禁用页面滚动
 */
export const disableBodyScroll = () => {
    document.body.style.overflow = 'hidden';
};

/**
 * 恢复页面滚动
 */
export const enableBodyScroll = () => {
    document.body.style.overflow = '';
};

// ==================== 波形和可视化相关 ====================

/**
 * 生成静态波形高度数组
 * @param count 波形条数量
 * @param minHeight 最小高度
 * @param maxHeight 最大高度
 * @returns 高度数组
 */
export const generateStaticWaveHeights = (
    count: number = AUDIO_CONFIG.WAVE_BARS,
    minHeight: number = AUDIO_CONFIG.MIN_HEIGHT,
    maxHeight: number = AUDIO_CONFIG.MAX_HEIGHT
): number[] => {
    return Array.from({ length: count }, () => {
        return minHeight + Math.random() * (maxHeight - minHeight);
    });
};

/**
 * 生成音频播放器的静态波形
 * @param count 波形条数量
 * @returns 高度数组
 */
export const generateAudioPlayerWaveHeights = (count: number = 60): number[] => {
    return Array.from({ length: count }, (_, index) => {
        // 创建更自然的波形模式
        const progress = index / (count - 1);
        const baseHeight = AUDIO_CONFIG.BASE_HEIGHT;
        const maxAdditional = AUDIO_CONFIG.MAX_ADDITIONAL_HEIGHT;

        // 使用正弦波和随机数结合创建更自然的波形
        const sineWave = Math.sin(progress * Math.PI * 4) * 0.5 + 0.5;
        const randomFactor = Math.random() * 0.6 + 0.4;
        const height = baseHeight + (sineWave * randomFactor * maxAdditional);

        return Math.max(baseHeight, Math.min(baseHeight + maxAdditional, height));
    });
};

/**
 * 处理音频频谱数据并转换为波形高度
 * @param frequencyData 频谱数据
 * @param waveCount 波形条数量
 * @param minHeight 最小高度
 * @param maxHeight 最大高度
 * @returns 波形高度数组
 */
export const processFrequencyDataToWave = (
    frequencyData: Uint8Array,
    waveCount: number = AUDIO_CONFIG.WAVE_BARS,
    minHeight: number = AUDIO_CONFIG.MIN_HEIGHT,
    maxHeight: number = AUDIO_CONFIG.MAX_HEIGHT
): number[] => {
    const heights: number[] = [];
    const dataLength = frequencyData.length;
    const step = Math.floor(dataLength / waveCount);

    for (let i = 0; i < waveCount; i++) {
        const startIndex = i * step;
        const endIndex = Math.min(startIndex + step, dataLength);

        // 计算这个区间的平均值
        let sum = 0;
        for (let j = startIndex; j < endIndex; j++) {
            sum += frequencyData[j];
        }
        const average = sum / (endIndex - startIndex);

        // 将0-255的值映射到minHeight-maxHeight范围
        const normalizedValue = average / 255;
        const height = minHeight + normalizedValue * (maxHeight - minHeight);

        heights.push(height);
    }

    return heights;
};

// ==================== 验证和工具方法 ====================

/**
 * 验证文本长度
 * @param text 文本内容
 * @param maxLength 最大长度
 * @returns 验证结果
 */
export const validateTextLength = (text: string | undefined, maxLength: number = 2500): {
    isValid: boolean;
    length: number;
    message?: string;
} => {
    const length = text?.length || 0;
    const isValid = length > 0 && length <= maxLength;

    let message: string | undefined;
    if (length === 0) {
        message = '请输入文本内容';
    } else if (length > maxLength) {
        message = `文本长度不能超过${maxLength}字符`;
    }

    return { isValid, length, message };
};

/**
 * 生成唯一的文件名
 * @param prefix 前缀
 * @param extension 扩展名
 * @returns 文件名
 */
export const generateUniqueFilename = (prefix: string = 'file', extension: string = 'wav'): string => {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${prefix}_${timestamp}_${random}.${extension}`;
};