# Edge浏览器麦克风兼容性修复

## 问题描述

在Edge浏览器中偶尔出现 `NotReadableError: Could not start audio source` 错误，导致麦克风检测失败。Chrome浏览器正常，麦克风硬件也正常。

## 问题原因分析

1. **Edge浏览器的严格音频处理机制**
   - Edge对音频设备的独占访问更严格
   - 对 `getUserMedia()` 的约束参数更敏感
   - 对采样率和声道数的支持可能有限制

2. **常见触发场景**
   - 音频设备被其他应用程序占用
   - 音频驱动与浏览器兼容性问题
   - 浏览器权限状态与系统权限不同步
   - 过于严格的音频约束参数

3. **NotReadableError的具体含义**
   - 音频设备无法读取（硬件层面）
   - 设备被其他进程占用
   - 驱动程序异常或不兼容

## 解决方案

### 1. 渐进式音频约束策略

实现了针对不同浏览器的渐进式约束策略：

**Edge浏览器（保守策略）：**
```javascript
[
    // 最基本的约束
    { audio: true },
    // 基本音频处理
    {
        audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
        }
    },
    // 标准采样率
    {
        audio: {
            sampleRate: 44100,
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
        }
    }
]
```

**其他浏览器（完整策略）：**
```javascript
[
    // 完整配置
    {
        audio: {
            channelCount: 1,
            sampleRate: 24000,
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
        }
    },
    // 降级策略...
]
```

### 2. 改进的错误处理

- 增加了浏览器类型检测
- 针对 `NotReadableError` 提供专门的错误信息
- 更详细的错误分类和用户提示

### 3. 新增工具函数

在 `utils/utils.ts` 中添加了：

- `getBrowserInfo()`: 检测浏览器类型
- `getAudioConstraints()`: 获取适合当前浏览器的音频约束

### 4. 改进的用户提示

更新了错误提示信息，包括：
- 关闭其他正在使用麦克风的应用程序
- 针对Edge浏览器的特殊建议
- 重启浏览器或系统的建议

## 代码变更

### 主要文件修改

1. **src/plugins/textToSpeech/components/CloneCom.vue**
   - 改进 `checkMicrophone()` 方法
   - 改进 `startRecording()` 方法
   - 更新错误提示信息

2. **src/plugins/textToSpeech/utils/utils.ts**
   - 新增 `getBrowserInfo()` 函数
   - 新增 `getAudioConstraints()` 函数

### 测试文件

创建了 `test-microphone.html` 用于测试不同浏览器的麦克风兼容性。

## 使用建议

### 对于用户

1. **Edge浏览器用户**：
   - 确保没有其他应用占用麦克风
   - 尝试重启浏览器
   - 考虑使用Chrome浏览器作为备选

2. **开发者**：
   - 使用渐进式约束策略
   - 提供详细的错误信息和解决方案
   - 考虑浏览器兼容性差异

### 测试方法

1. 打开 `test-microphone.html` 进行兼容性测试
2. 在不同浏览器中测试麦克风功能
3. 模拟音频设备被占用的场景

## 预期效果

- 显著减少Edge浏览器中的 `NotReadableError` 发生率
- 提高麦克风检测的成功率
- 提供更好的用户体验和错误提示
- 保持与其他浏览器的兼容性

## 后续优化建议

1. 添加音频设备状态监控
2. 实现自动重试机制
3. 添加音频质量检测
4. 考虑使用WebRTC的更高级API
